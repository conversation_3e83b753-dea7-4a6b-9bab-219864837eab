# AI Agent Prompt for Dota 2 Model Training

## 🎯 **Primary Training Prompt**

```
You are working on the Dota 2 Match Result Predictor project. Your task is to train the machine learning model using the Kaggle dataset and GPU acceleration.

**Primary Objective**: Follow the recommended training method to produce an updated `xgb_model.pkl`.

**Key Documentation**:
- **Full Guide**: `docs/training/MODEL_TRAINING_GUIDE.md`
- **Quick Commands**: `docs/training/TRAINING_QUICK_START.md`
- **System Guidance**: `.clinerules/`

**Core Requirements**:
1.  **Activate Environment**: `mamba activate dota2predictor-env`
2.  **Verify GPU**: Run `python verify_gpu.py` to ensure the RTX 4070 is ready.
3.  **Use Recommended Method**: Follow "Method 1: Kaggle Dataset" from the training guides.
4.  **Validate Data**: Ensure the Kaggle dataset in `dataset/kaggle_data/` is correctly structured and run `python dataset/validate_training_data.py`.
5.  **Execute Training**: Use the scripts as defined in the quick start guide to process data and train the model.
6.  **Monitor**: Keep an eye on GPU utilization (>80%) and performance metrics.

**For detailed steps, troubleshooting, and feature explanations, refer to the full `MODEL_TRAINING_GUIDE.md`**.

EXPECTED WORKFLOW:
1. Activate environment: mamba activate dota2predictor-env
2. Validate setup and GPU functionality
3. Verify Kaggle dataset structure (5+ years of data)
4. Process comprehensive dataset with create_training_data_from_kaggle.py
5. Train model with GPU acceleration using 370+ features
6. Verify model performance and GPU utilization

DATA EXPECTATIONS:
- 10,000+ professional matches from 2020-2025
- 370+ features per match including player-hero win rates
- GPU-accelerated training should be 10-50x faster than CPU
- Training data will be saved to dataset/train_data/all_data_match_predict.csv

Please proceed with the training process, following the updated guide exactly, and report any issues or successful completion with performance metrics including GPU utilization.
```

## 🔧 **Dataset Structure Validation Prompt**

```
Before starting training, validate the Kaggle dataset structure:

EXPECTED STRUCTURE:
dataset/kaggle_data/
├── 2022/ (complete 2022 professional matches)
├── 2023/ (complete 2023 professional matches)
├── 2024/ (complete 2024 professional matches)
├── 202501/ (January 2025 matches)
├── 202502/ (February 2025 matches)
├── 202503/ (March 2025 matches)
├── 202504/ (April 2025 matches)
├── 202505/ (May 2025 matches)
├── 202506/ (June 2025 matches)
├── 202507/ (July 2025 matches)
└── Constants/ (hero/item definitions)

REQUIRED FILES IN EACH DIRECTORY:
- main_metadata.csv (match outcomes and metadata)
- players.csv (player statistics for each match)
- picks_bans.csv (hero picks and bans)
- teams.csv (team information)
- objectives.csv (game objectives)
- draft_timings.csv (pick/ban timing)

VALIDATION COMMANDS:
1. python validate_training_setup.py
2. Check dataset structure manually if needed
3. Verify file sizes are reasonable (players.csv should be largest)

If dataset structure is incorrect, provide specific guidance on fixing it.
```

## 🚀 **Performance Monitoring Prompt**

```
During training, monitor and report:

GPU UTILIZATION:
- Use nvidia-smi to monitor GPU usage (should be >80% during training)
- Training should be 10-50x faster than CPU-only training
- Watch for CUDA out-of-memory errors (reduce batch size if needed)
- Verify XGBoost is using GPU with device='cuda' parameter

DATA PROCESSING:
- Script should process multiple years of data automatically
- Expected to find 10,000+ professional matches
- Player-hero win rates calculated from comprehensive dataset
- 370+ features generated per match

TRAINING METRICS:
- Report training time with GPU acceleration
- Monitor model accuracy and validation scores
- Check for overfitting with early stopping
- Verify model files are saved correctly (xgb_model.pkl, scaler.pkl)

EXPECTED OUTPUTS:
- dataset/train_data/all_data_match_predict.csv (comprehensive training data)
- xgb_model.pkl (trained XGBoost model with GPU optimization)
- scaler.pkl (feature scaler)
- Training logs with GPU utilization and performance metrics
```

## 🛠️ **Troubleshooting Prompt**

```
If you encounter issues during training:

ENVIRONMENT ISSUES:
1. Check .clinerules/quick_reference.md for debugging steps
2. Review .clinerules/technical_reference.md for implementation patterns
3. Use the troubleshooting section in MODEL_TRAINING_GUIDE.md
4. Run validate_training_setup.py to diagnose environment issues

DATASET ISSUES:
- Verify Kaggle dataset is extracted to correct location
- Check that both annual (2020-2024) and monthly (202501-202507) folders exist
- Ensure required CSV files exist in each directory
- Validate file sizes are reasonable (players.csv typically largest)

GPU ISSUES:
- Check GPU status with nvidia-smi if training fails
- Verify CUDA 12.4 compatibility
- Check XGBoost GPU installation
- Use CPU fallback if GPU training fails

DATA PROCESSING ISSUES:
- Check create_training_data_from_kaggle.py logs for errors
- Verify start_year parameter (default: 2023)
- Ensure sufficient disk space for processing
- Monitor memory usage during data processing

Report specific error messages and system state for targeted troubleshooting.
```

## 📊 **Success Validation Prompt**

```
After training completion, validate success:

MODEL VALIDATION:
1. Run python verify_gpu.py to test the trained model
2. Check model file sizes and timestamps
3. Verify prediction accuracy on test data
4. Test bot functionality with new model

PERFORMANCE VALIDATION:
- Confirm GPU acceleration was used during training
- Report training time improvement over CPU
- Validate model performance metrics
- Check feature importance and model interpretability

INTEGRATION VALIDATION:
- Test model loading in MainML class
- Verify scaler compatibility
- Test prediction pipeline end-to-end
- Confirm bot can use new model for predictions

SUCCESS CRITERIA:
✅ GPU-accelerated training completed successfully
✅ Model accuracy meets or exceeds previous versions
✅ Training time significantly reduced with GPU
✅ Model files saved correctly and load properly
✅ Bot functionality verified with new model
✅ Comprehensive dataset (5+ years) processed successfully

Report final metrics, training time, and any performance improvements achieved.
```

This prompt ensures the AI agent follows the standardized, GPU-optimized training process using the consolidated documentation.
